<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Components Demo - Material 3 重構版</title>
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            display: grid;
            gap: 2rem;
        }

        .demo-section {
            padding: 1.5rem;
            border-radius: 12px;
            border: 1px solid var(--md-sys-color-outline);
            background-color: var(--md-sys-color-surface-container);
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
            margin-top: 1rem;
        }

        .component-showcase {
            padding: 1rem;
            border-radius: 8px;
            background-color: var(--md-sys-color-surface);
            border: 1px solid var(--md-sys-color-outline);
        }
    </style>
</head>
<body class="light-theme">
    <!-- 主題切換按鈕 -->
    <button class="theme-toggle-btn" onclick="toggleTheme()" title="切換主題">
        🌙
    </button>

    <div class="demo-container">
        <header>
            <h1 class="md-headline-large" style="color: var(--md-sys-color-primary); margin-bottom: 0.5rem;">
                Components Demo
            </h1>
            <p class="md-body-large text-secondary">
                重構後的組件展示 - 基於 Material 3 Design Tokens
            </p>
        </header>

        <!-- 按鈕展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">按鈕組件</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">主要按鈕</h3>
                    <button class="btn btn-primary">Primary Button</button>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">次要按鈕</h3>
                    <button class="btn btn-secondary">Secondary Button</button>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">按鈕尺寸</h3>
                    <button class="btn btn-primary btn-sm">Small</button>
                    <button class="btn btn-primary">Medium</button>
                    <button class="btn btn-primary btn-lg">Large</button>
                </div>
            </div>
        </section>

        <!-- Input 組件圖標增強展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">Input 組件圖標增強</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">密碼輸入框 (眼睛圖標)</h3>
                    <label class="input-label">密碼</label>
                    <input type="password" class="input-text input-text-with-icon" placeholder="請輸入密碼" id="passwordInput1">

                    <label class="input-label" style="margin-top: 1rem;">確認密碼</label>
                    <input type="password" class="input-text input-text-with-icon" placeholder="請確認密碼" id="passwordInput2">

                    <label class="input-label" style="margin-top: 1rem;">顯示密碼狀態</label>
                    <input type="text" class="input-text input-text-with-icon password-hidden" placeholder="密碼已顯示" value="mypassword123">
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">搜尋下拉選單 (放大鏡圖標)</h3>
                    <label class="input-label">語言搜尋</label>
                    <select class="input-select input-select-with-search">
                        <option>搜尋語言...</option>
                        <option>繁體中文</option>
                        <option>简体中文</option>
                        <option>English</option>
                        <option>日本語</option>
                        <option>한국어</option>
                    </select>

                    <label class="input-label" style="margin-top: 1rem;">已選擇狀態</label>
                    <select class="input-select input-select-with-search input-done">
                        <option>繁體中文</option>
                        <option>简体中文</option>
                        <option>English</option>
                    </select>

                    <label class="input-label" style="margin-top: 1rem;">聚焦狀態 (點擊查看)</label>
                    <select class="input-select input-select-with-search">
                        <option>點擊查看聚焦效果</option>
                        <option>選項 A</option>
                        <option>選項 B</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- Input 組件擴展展示 (原版) -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">Input 組件擴展 (原版)</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">文字輸入框狀態</h3>
                    <label class="input-label">預設狀態</label>
                    <input type="text" class="input-text" placeholder="請輸入文字...">

                    <label class="input-label" style="margin-top: 1rem;">輸入中狀態</label>
                    <input type="text" class="input-text input-doing" placeholder="正在輸入..." value="正在輸入的文字">

                    <label class="input-label required" style="margin-top: 1rem;">錯誤狀態</label>
                    <input type="text" class="input-text error" placeholder="此欄位有錯誤" value="錯誤的輸入">
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">下拉清單狀態</h3>
                    <label class="input-label">預設狀態</label>
                    <select class="input-select">
                        <option>請選擇選項...</option>
                        <option>選項 1</option>
                        <option>選項 2</option>
                    </select>

                    <label class="input-label" style="margin-top: 1rem;">已選擇狀態</label>
                    <select class="input-select input-done">
                        <option>已選擇的選項</option>
                        <option>其他選項</option>
                    </select>

                    <label class="input-label" style="margin-top: 1rem;">懸停狀態 (請懸停)</label>
                    <select class="input-select">
                        <option>懸停查看效果</option>
                        <option>選項 A</option>
                        <option>選項 B</option>
                    </select>
                </div>
            </div>
        </section>

        <!-- 表單組件展示 (舊版保留) -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">表單組件 (舊版)</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">輸入框</h3>
                    <input type="text" class="input" placeholder="請輸入文字...">
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">表單控制項</h3>
                    <select class="form-control">
                        <option>選項 1</option>
                        <option>選項 2</option>
                        <option>選項 3</option>
                    </select>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">搜尋選擇</h3>
                    <div class="search-input-container">
                        <span class="search-icon">🔍</span>
                        <select class="form-control search-select">
                            <option>搜尋選項...</option>
                            <option>中文</option>
                            <option>English</option>
                            <option>日本語</option>
                        </select>
                    </div>
                </div>
            </div>
        </section>

        <!-- AppBar 組件展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">AppBar 組件</h2>
            <div class="component-showcase">
                <div class="app-bar">
                    <div class="app-bar-left">
                        <span>🔍 搜尋功能</span>
                    </div>
                    <div class="app-bar-center">
                        <h3 style="margin: 0;">應用標題</h3>
                    </div>
                    <div class="app-bar-right">
                        <button class="btn btn-primary btn-sm">新增資料</button>
                    </div>
                </div>
            </div>
        </section>

        <!-- ToolBar 組件展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">ToolBar 組件</h2>
            <div class="component-showcase">
                <div class="tool-bar">
                    <span class="tab-item tab-item-active">首頁</span>
                    <span class="tab-item tab-item-inactive">產品</span>
                    <span class="tab-item tab-item-inactive">服務</span>
                    <span class="tab-item tab-item-inactive">關於我們</span>
                    <div class="language-switcher">
                        <span class="tab-item tab-item-inactive">中文</span>
                        <span class="tab-item tab-item-inactive">English</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- 新版 Chip 組件展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">Chip 組件 (新版)</h2>
            <div class="component-showcase">
                <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                    <span class="chip chip-removable">
                        React
                        <button class="chip-remove-btn" type="button">&times;</button>
                    </span>
                    <span class="chip chip-removable">
                        Vue.js
                        <button class="chip-remove-btn" type="button">&times;</button>
                    </span>
                    <span class="chip chip-removable">
                        TypeScript
                        <button class="chip-remove-btn" type="button">&times;</button>
                    </span>
                    <span class="chip">
                        CSS (不可移除)
                    </span>
                </div>
            </div>
        </section>

        <!-- 語言標籤展示 (舊版保留) -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">語言標籤 (舊版)</h2>
            <div class="selected-languages">
                <span class="language-pill">
                    中文
                    <button class="remove-pill" type="button">&times;</button>
                </span>
                <span class="language-pill">
                    English
                    <button class="remove-pill" type="button">&times;</button>
                </span>
                <span class="language-pill">
                    日本語
                    <button class="remove-pill" type="button">&times;</button>
                </span>
            </div>
        </section>

        <!-- 卡片展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">卡片組件</h2>
            <div class="demo-grid">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">卡片標題</h3>
                    </div>
                    <div class="card-content">
                        這是卡片內容，展示了重構後的樣式效果。
                    </div>
                </div>
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">另一個卡片</h3>
                    </div>
                    <div class="card-content">
                        支援主題切換的卡片組件。
                    </div>
                </div>
            </div>
        </section>

        <!-- 訊息展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">狀態訊息</h2>
            <div style="display: grid; gap: 1rem;">
                <div class="message message-success">
                    ✅ 成功訊息 - 操作已完成
                </div>
                <div class="message message-warning">
                    ⚠️ 警告訊息 - 請注意相關事項
                </div>
                <div class="message message-error">
                    ❌ 錯誤訊息 - 發生了錯誤
                </div>
                <div class="message message-info">
                    ℹ️ 資訊訊息 - 提供額外資訊
                </div>
            </div>
        </section>

        <!-- 工具類別展示 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">工具類別</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">文字顏色</h3>
                    <p class="text-primary">主要文字顏色</p>
                    <p class="text-secondary">次要文字顏色</p>
                    <p class="text-tertiary">第三級文字顏色</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">字重</h3>
                    <p class="font-light">輕字重</p>
                    <p class="font-normal">標準字重</p>
                    <p class="font-medium">中等字重</p>
                    <p class="font-bold">粗字重</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">連結</h3>
                    <a href="#" class="link">這是一個連結</a>
                </div>
            </div>
        </section>

        <!-- 使用說明 -->
        <section class="demo-section">
            <h2 class="md-title-large" style="margin-bottom: 1rem;">重構特色</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="md-title-small">🎨 主題切換</h3>
                    <p class="md-body-medium">點擊右上角按鈕切換明暗主題</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">📱 響應式</h3>
                    <p class="md-body-medium">字體大小自動適應螢幕尺寸</p>
                </div>
                <div class="component-showcase">
                    <h3 class="md-title-small">🔧 Material 3</h3>
                    <p class="md-body-medium">基於 Material 3 Design Tokens</p>
                </div>
            </div>
        </section>

        <!-- 結束會議確認卡片組件 -->
        <section class="demo-section">
            <h2 class="md-headline-medium" style="color: var(--md-sys-color-on-surface); margin-bottom: 1rem;">
                結束會議確認卡片
            </h2>
            <p class="md-body-medium" style="color: var(--md-sys-color-on-surface-variant); margin-bottom: 1.5rem;">
                基於 Material 3 設計系統的確認對話框組件，用於重要操作的二次確認。
            </p>

            <div class="component-showcase">
                <h3 class="md-title-medium" style="margin-bottom: 1rem;">基本用法</h3>
                <button class="btn btn-primary" onclick="showEndMeetingDialog()">
                    結束會議
                </button>

                <div class="demo-code" style="margin-top: 1rem;">
                    <h4 class="md-title-small">HTML 結構：</h4>
                    <pre style="background: var(--md-sys-color-surface-variant); padding: 1rem; border-radius: 8px; overflow-x: auto;"><code>&lt;!-- 結束會議確認卡片 --&gt;
&lt;div class="end-meeting-overlay hidden" id="endMeetingOverlay"&gt;
    &lt;div class="end-meeting-card"&gt;
        &lt;h3 class="end-meeting-title"&gt;確定結束會議？&lt;/h3&gt;
        &lt;div class="end-meeting-actions"&gt;
            &lt;button class="btn btn-cancel" onclick="hideEndMeetingDialog()"&gt;
                返回會議
            &lt;/button&gt;
            &lt;button class="btn btn-danger" onclick="confirmEndMeeting()"&gt;
                確定結束
            &lt;/button&gt;
        &lt;/div&gt;
    &lt;/div&gt;
&lt;/div&gt;</code></pre>
                </div>
            </div>
        </section>
    </div>

    <!-- 結束會議確認卡片 -->
    <div class="end-meeting-overlay hidden" id="endMeetingOverlay">
        <div class="end-meeting-card">
            <h3 class="end-meeting-title">確定結束會議？</h3>
            <div class="end-meeting-actions">
                <button class="btn btn-cancel" onclick="hideEndMeetingDialog()">
                    返回會議
                </button>
                <button class="btn btn-danger" onclick="confirmEndMeeting()">
                    確定結束
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleTheme() {
            const body = document.body;
            const btn = document.querySelector('.theme-toggle-btn');
            
            if (body.classList.contains('light-theme')) {
                body.classList.remove('light-theme');
                body.classList.add('dark-theme');
                btn.textContent = '☀️';
                btn.title = '切換到明亮主題';
            } else {
                body.classList.remove('dark-theme');
                body.classList.add('light-theme');
                btn.textContent = '🌙';
                btn.title = '切換到深色主題';
            }
        }

        // 結束會議確認卡片功能
        function showEndMeetingDialog() {
            const overlay = document.getElementById('endMeetingOverlay');
            overlay.classList.remove('hidden');
            // 使用 setTimeout 確保 DOM 更新後再添加 show 類別
            setTimeout(() => {
                overlay.classList.add('show');
            }, 10);
        }

        function hideEndMeetingDialog() {
            const overlay = document.getElementById('endMeetingOverlay');
            overlay.classList.remove('show');
            // 等待動畫完成後隱藏元素
            setTimeout(() => {
                overlay.classList.add('hidden');
            }, 300);
        }

        function confirmEndMeeting() {
            // 這裡可以添加實際的結束會議邏輯
            alert('會議已結束！');
            hideEndMeetingDialog();
        }

        // 點擊遮罩層關閉對話框
        document.getElementById('endMeetingOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                hideEndMeetingDialog();
            }
        });

        // ESC 鍵關閉對話框
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const overlay = document.getElementById('endMeetingOverlay');
                if (!overlay.classList.contains('hidden')) {
                    hideEndMeetingDialog();
                }
            }
        });

        // 移除按鈕功能 (舊版語言標籤)
        document.querySelectorAll('.remove-pill').forEach(btn => {
            btn.addEventListener('click', function() {
                this.parentElement.remove();
            });
        });

        // 新版 Chip 組件移除功能
        document.querySelectorAll('.chip-remove-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                this.parentElement.remove();
            });
        });

        // 密碼顯示/隱藏功能
        document.querySelectorAll('.input-text-with-icon').forEach(input => {
            input.addEventListener('click', function(e) {
                const rect = this.getBoundingClientRect();
                const clickX = e.clientX - rect.left;
                const iconStart = rect.width - 40; // 圖標區域大約40px

                // 如果點擊在圖標區域
                if (clickX > iconStart) {
                    if (this.type === 'password') {
                        this.type = 'text';
                        this.classList.add('password-hidden');
                    } else {
                        this.type = 'password';
                        this.classList.remove('password-hidden');
                    }
                }
            });
        });
    </script>
</body>
</html>
