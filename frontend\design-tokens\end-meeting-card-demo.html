<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>結束會議確認卡片 - Material 3 組件</title>
    <link rel="stylesheet" href="components.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />
    <style>
        body {
            margin: 0;
            padding: 2rem;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Noto Sans', 'Segoe UI', sans-serif;
        }

        .demo-container {
            background-color: var(--md-sys-color-surface);
            border-radius: var(--md-sys-shape-corner-large);
            padding: 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }

        .demo-title {
            font-size: var(--md-sys-typescale-headline-medium-size);
            font-weight: 500;
            color: var(--md-sys-color-on-surface);
            margin-bottom: 1rem;
        }

        .demo-description {
            font-size: var(--md-sys-typescale-body-medium-size);
            color: var(--md-sys-color-on-surface-variant);
            margin-bottom: 2rem;
            line-height: 1.6;
        }

        .demo-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .theme-toggle {
            position: fixed;
            top: 1rem;
            right: 1rem;
            background: var(--md-sys-color-primary);
            color: var(--md-sys-color-on-primary);
            border: none;
            border-radius: 50%;
            width: 48px;
            height: 48px;
            font-size: 1.2rem;
            cursor: pointer;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
            transition: all 0.2s ease;
        }

        .theme-toggle:hover {
            transform: scale(1.1);
        }

        .feature-list {
            text-align: left;
            margin: 2rem 0;
            padding: 1.5rem;
            background-color: var(--md-sys-color-surface-container);
            border-radius: var(--md-sys-shape-corner-medium);
        }

        .feature-list h3 {
            color: var(--md-sys-color-on-surface);
            margin-bottom: 1rem;
        }

        .feature-list ul {
            color: var(--md-sys-color-on-surface-variant);
            line-height: 1.6;
        }

        .feature-list li {
            margin-bottom: 0.5rem;
        }
    </style>
</head>
<body class="light-theme">
    <!-- 主題切換按鈕 -->
    <button class="theme-toggle" onclick="toggleTheme()" title="切換主題">
        🌙
    </button>

    <div class="demo-container">
        <h1 class="demo-title">結束會議確認卡片</h1>
        <p class="demo-description">
            基於 Material 3 設計系統的確認對話框組件，提供優雅的用戶體驗和完整的互動功能。
        </p>

        <div class="feature-list">
            <h3>✨ 組件特色</h3>
            <ul>
                <li>🎨 完全符合 Material 3 設計規範</li>
                <li>🌓 支援明暗主題自動切換</li>
                <li>📱 響應式設計，適配各種螢幕尺寸</li>
                <li>⌨️ 支援鍵盤操作（ESC 鍵關閉）</li>
                <li>🖱️ 支援點擊遮罩層關閉</li>
                <li>✨ 流暢的進入和退出動畫</li>
                <li>🎯 清晰的視覺層次和操作引導</li>
            </ul>
        </div>

        <div class="demo-actions">
            <button class="btn btn-primary" onclick="showEndMeetingDialog()">
                <span class="material-symbols-outlined" style="margin-right: 0.5rem;">videocam_off</span>
                結束會議
            </button>
            <button class="btn btn-secondary" onclick="showCustomDialog()">
                自定義測試
            </button>
        </div>
    </div>

    <!-- 結束會議確認卡片 -->
    <div class="end-meeting-overlay hidden" id="endMeetingOverlay">
        <div class="end-meeting-card">
            <h3 class="end-meeting-title">確定結束會議？</h3>
            <div class="end-meeting-actions">
                <button class="btn btn-cancel" onclick="hideEndMeetingDialog()">
                    返回會議
                </button>
                <button class="btn btn-danger" onclick="confirmEndMeeting()">
                    確定結束
                </button>
            </div>
        </div>
    </div>

    <!-- 自定義測試對話框 -->
    <div class="end-meeting-overlay hidden" id="customDialog">
        <div class="end-meeting-card">
            <h3 class="end-meeting-title">這是自定義標題</h3>
            <div class="end-meeting-actions">
                <button class="btn btn-cancel" onclick="hideCustomDialog()">
                    取消
                </button>
                <button class="btn btn-danger" onclick="confirmCustomAction()">
                    確認操作
                </button>
            </div>
        </div>
    </div>

    <script>
        // 主題切換功能
        function toggleTheme() {
            const body = document.body;
            const themeToggle = document.querySelector('.theme-toggle');
            
            if (body.classList.contains('light-theme')) {
                body.classList.remove('light-theme');
                body.classList.add('dark-theme');
                themeToggle.textContent = '☀️';
                themeToggle.title = '切換到明亮主題';
            } else {
                body.classList.remove('dark-theme');
                body.classList.add('light-theme');
                themeToggle.textContent = '🌙';
                themeToggle.title = '切換到深色主題';
            }
        }

        // 結束會議確認卡片功能
        function showEndMeetingDialog() {
            showDialog('endMeetingOverlay');
        }

        function hideEndMeetingDialog() {
            hideDialog('endMeetingOverlay');
        }

        function confirmEndMeeting() {
            // 模擬結束會議的過程
            const overlay = document.getElementById('endMeetingOverlay');
            const card = overlay.querySelector('.end-meeting-card');
            const title = card.querySelector('.end-meeting-title');
            const actions = card.querySelector('.end-meeting-actions');
            
            title.textContent = '會議已結束';
            actions.innerHTML = '<button class="btn btn-primary" onclick="hideEndMeetingDialog()">確定</button>';
            
            setTimeout(() => {
                hideEndMeetingDialog();
                // 重置對話框內容
                setTimeout(() => {
                    title.textContent = '確定結束會議？';
                    actions.innerHTML = `
                        <button class="btn btn-cancel" onclick="hideEndMeetingDialog()">返回會議</button>
                        <button class="btn btn-danger" onclick="confirmEndMeeting()">確定結束</button>
                    `;
                }, 300);
            }, 1500);
        }

        // 自定義對話框功能
        function showCustomDialog() {
            showDialog('customDialog');
        }

        function hideCustomDialog() {
            hideDialog('customDialog');
        }

        function confirmCustomAction() {
            alert('自定義操作已執行！');
            hideCustomDialog();
        }

        // 通用對話框顯示/隱藏函數
        function showDialog(dialogId) {
            const overlay = document.getElementById(dialogId);
            overlay.classList.remove('hidden');
            setTimeout(() => {
                overlay.classList.add('show');
            }, 10);
        }

        function hideDialog(dialogId) {
            const overlay = document.getElementById(dialogId);
            overlay.classList.remove('show');
            setTimeout(() => {
                overlay.classList.add('hidden');
            }, 300);
        }

        // 全局事件監聽器
        document.addEventListener('DOMContentLoaded', function() {
            // 點擊遮罩層關閉對話框
            document.querySelectorAll('.end-meeting-overlay').forEach(overlay => {
                overlay.addEventListener('click', function(e) {
                    if (e.target === this) {
                        hideDialog(this.id);
                    }
                });
            });

            // ESC 鍵關閉對話框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    document.querySelectorAll('.end-meeting-overlay:not(.hidden)').forEach(overlay => {
                        hideDialog(overlay.id);
                    });
                }
            });
        });
    </script>
</body>
</html>
