<!DOCTYPE html>
<html lang="zh-Hant">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即時翻譯 - 與會者頁面</title>

    <!-- 設計權杖和組件樣式 -->
    <link rel="stylesheet" href="design-tokens/tokens.css">
    <link rel="stylesheet" href="design-tokens/components.css">
    <link rel="stylesheet" href="style.css">

    <!-- 字體載入 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Noto+Sans:wght@100;300;400;500;600;700;800;900&display=swap">

    <!-- Material Symbols 圖示 -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200" />

    <style>
        /* ===== 基礎設定 ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans', sans-serif;
            background-color: var(--md-ref-palette-neutral-50);
            color: var(--md-ref-palette-neutral-900);
            line-height: 1.6;
            min-height: 100vh;
        }

        /* ===== 主要佈局 ===== */
        .attendee-page {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            padding: 0 16px;
        }
        

        /* ===== Header 區域 ===== */
        .tool-bar {
            padding: 0;
            margin: 0;
        }
        .app-bar.no-btn {
            justify-content: center;
            align-items: center;
            padding: 1rem;
        }
        .app-bar.no-btn h1{
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        /* ===== Language Selector 區域 ===== */
        .language-tags {
            display: flex;
            gap: 32px;
            justify-content: center;
            align-items: center;
            border: none;
            background: none;
            box-shadow: none;
        }

        .language-tag {
            padding: 12px 0;
            color: var(--md-ref-palette-neutral-600);
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            user-select: none;
            position: relative;
            border: none;
            background: none;
        }

        .language-tag:hover {
            color: var(--md-ref-palette-primary-600);
            background: none;
        }

        .language-tag.tab-item-active {
            color: var(--md-ref-palette-neutral-900);
            font-weight: 600;
        }
        .tab-item:hover:not(.tab-item-active){
            background: none;
        }

        .language-tag.tab-item-active::after {
            content: '';
            position: absolute;
            bottom: -1px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: var(--md-ref-palette-primary-600);
            border-radius: 1px;
        }

        /* ===== Translation Display Area 區域 ===== */
        .translation-display-area {
            flex: 1;
            padding-top: 2rem;
            max-width: 960px;
            width: 100%;
            position: relative;
            overflow: hidden;
        }

        .translation-display-area__welcome {
            font-size: 16px;
            color: var(--md-ref-palette-neutral-600);
            margin-bottom: 24px;
            text-align: left;
            opacity: 0.8;
            animation: fadeInText 0.8s ease-in-out;
        }

        .translation-display-area__instructions {
            font-size: 14px;
            line-height: 1.8;
            margin-bottom: 24px;
            position: relative;
        }

        .translation-display-area__instructions p {
            margin-bottom: 16px;
            opacity: 0;
            animation: fadeInText 0.8s ease-in-out forwards;
            background: linear-gradient(180deg,
                    var(--md-ref-palette-neutral-400) 0%,
                    var(--md-ref-palette-neutral-600) 30%,
                    var(--md-ref-palette-neutral-700) 70%,
                    var(--md-ref-palette-neutral-800) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-size: 100% 200%;
            animation: fadeInText 0.8s ease-in-out forwards,
                textGradientFlow 3s ease-in-out infinite;
        }

        .translation-display-area__instructions p:nth-child(1) {
            animation-delay: 0.2s;
        }

        .translation-display-area__instructions p:nth-child(2) {
            animation-delay: 0.4s;
        }

        .translation-display-area__instructions p:nth-child(3) {
            animation-delay: 0.6s;
        }

        .translation-display-area__highlight {
            font-weight: 600;
            font-size: 15px;
            opacity: 0;
            animation: fadeInText 0.8s ease-in-out 0.8s forwards;
            background: linear-gradient(135deg,
                    var(--md-ref-palette-neutral-800) 0%,
                    var(--md-ref-palette-neutral-900) 50%,
                    var(--md-ref-palette-primary-700) 100%);
            background-clip: text;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        /* 動畫效果 */
        @keyframes fadeInText {
            from {
                opacity: 0;
                transform: translateY(10px);
            }

            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes textGradientFlow {

            0%,
            100% {
                background-position: 0% 0%;
            }

            50% {
                background-position: 0% 100%;
            }
        }

        /* ===== Live Transcript 區域 ===== */
        .live-transcript {
            border-top: 1px solid var(--md-ref-palette-neutral-200);
            padding: 24px 0;
            margin-top: auto;
        }

        .live-transcript__title {
            font-size: 18px;
            font-weight: 600;
            color: var(--md-ref-palette-neutral-900);
            margin-bottom: 16px;
            text-align: center;
        }

        .live-transcript__content {
            background-color: var(--md-ref-palette-neutral-100);
            border-radius: 8px;
            padding: 16px;
            min-height: 120px;
            max-width: 800px;
            margin: 0 auto;
        }

        .live-transcript__text {
            font-size: 14px;
            color: var(--md-ref-palette-neutral-800);
            line-height: 1.6;
        }

        /* ===== 響應式設計 ===== */
        @media (max-width: 767px) {
            .attendee-page {
                padding: 0 12px;
            }

            .header {
                padding: 24px 0;
            }

            .header__title {
                font-size: 20px;
            }

            .language-tags {
                gap: 20px;
            }

            .language-tag {
                padding: 10px 0;
                font-size: 13px;
            }

            .translation-display-area {
                padding: 24px 0;
            }

            .translation-display-area__welcome {
                font-size: 15px;
            }

            .translation-display-area__instructions {
                font-size: 13px;
            }

            .translation-display-area__instructions p {
                margin-bottom: 14px;
            }
        }

        @media (min-width: 768px) and (max-width: 1023px) {
            .attendee-page {
                padding: 0 24px;
            }
        }

        @media (min-width: 1024px) {
            .attendee-page {
                padding: 0 32px;
            }

            .header {
                padding: 48px 0;
            }
        }
    </style>
</head>

<body class="light-theme">
    <main class="bg-primary attendee-page">
        <!-- 1. Header 區域 -->
        <section class="control-buttons">
            <div class="app-bar no-btn">
                <div class="logo-container">
                    <img src="images/Logo.png" alt="logo-image">
                </div>
                <header class="app-header">
                    <h1>即時翻譯</h1>
                </header>
            </div>
        </section>

        <!-- 2. Language Selector 區域 -->
        <section class="tool-bar">
            <div class="language-tags">
                <span class="tab-item tab-item-active language-tag" data-lang="en">English</span>
                <span class="tab-item language-tag" data-lang="ja">日本語</span>
                <span class="tab-item language-tag" data-lang="zh">繁體中文</span>
                <span class="tab-item language-tag" data-lang="ko">한국어</span>
            </div>
        </section>

        <!-- 3. Translation Display Area 區域 -->
        <section class="translation-display-area">
            <div class="translation-display-area__welcome">
                Distinguished guests, welcome to this international conference.
            </div>
            <div class="translation-display-area__instructions">
                <p>To ensure the smooth progress of the meeting, please set your mobile phones to silent or vibrate
                    mode.</p>
                <p>During the meeting, please refrain from moving around or engaging in conversation.</p>
                <p>Thank you for your cooperation. Today's session is expected to last until around 4 PM.</p>
                <p class="translation-display-area__highlight">If you have any questions, feel free to ask the staff
                    nearby.</p>
            </div>
        </section>

        <!-- 4. 即時字幕區域 -->
        <section class="live-subtitles">
            <h2 class="subtitle-title">Live Transcript</h2>
            <div class="subtitle-content">
                <p>各位貴賓，歡迎蒞臨本次國際會議。為了確保會議順利進行，請務必保持手機靜音或震動模式。會議期間請勿隨意走動或交談，感謝配合。今天的會議預計到四點左右，隨時有問題都可以向身旁的工作人員詢問。</p>
            </div>
        </section>
    </main>
</body>

</html>