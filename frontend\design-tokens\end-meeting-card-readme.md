# 結束會議確認卡片組件

基於 Material 3 設計系統的確認對話框組件，專為重要操作的二次確認而設計。

## 🎯 設計特色

- **Material 3 設計規範**：完全符合 Google Material 3 設計系統
- **響應式設計**：適配桌面端和移動端設備
- **主題支援**：支援明暗主題自動切換
- **無障礙設計**：支援鍵盤操作和螢幕閱讀器
- **流暢動畫**：優雅的進入和退出過渡效果

## 📁 文件結構

```
design-tokens/
├── components.css                    # 主要樣式文件（包含組件樣式）
├── components-demo.html             # 完整組件演示頁面
├── end-meeting-card-demo.html       # 獨立組件演示頁面
└── end-meeting-card-readme.md       # 使用說明文檔
```

## 🚀 快速開始

### 1. 基本 HTML 結構

```html
<!-- 結束會議確認卡片 -->
<div class="end-meeting-overlay hidden" id="endMeetingOverlay">
    <div class="end-meeting-card">
        <h3 class="end-meeting-title">確定結束會議？</h3>
        <div class="end-meeting-actions">
            <button class="btn btn-cancel" onclick="hideEndMeetingDialog()">
                返回會議
            </button>
            <button class="btn btn-danger" onclick="confirmEndMeeting()">
                確定結束
            </button>
        </div>
    </div>
</div>
```

### 2. 觸發按鈕

```html
<button class="btn btn-primary" onclick="showEndMeetingDialog()">
    結束會議
</button>
```

### 3. JavaScript 功能

```javascript
// 顯示對話框
function showEndMeetingDialog() {
    const overlay = document.getElementById('endMeetingOverlay');
    overlay.classList.remove('hidden');
    setTimeout(() => {
        overlay.classList.add('show');
    }, 10);
}

// 隱藏對話框
function hideEndMeetingDialog() {
    const overlay = document.getElementById('endMeetingOverlay');
    overlay.classList.remove('show');
    setTimeout(() => {
        overlay.classList.add('hidden');
    }, 300);
}

// 確認操作
function confirmEndMeeting() {
    // 在這裡添加實際的結束會議邏輯
    alert('會議已結束！');
    hideEndMeetingDialog();
}
```

## 🎨 CSS 類別說明

### 主要容器類別

- `.end-meeting-overlay`：遮罩層容器
- `.end-meeting-card`：卡片主體
- `.end-meeting-title`：標題樣式
- `.end-meeting-actions`：按鈕容器

### 按鈕樣式類別

- `.btn-cancel`：取消按鈕（藍色）
- `.btn-danger`：危險操作按鈕（紅色）

### 狀態類別

- `.hidden`：隱藏狀態
- `.show`：顯示狀態（帶動畫）

## ⚙️ 自定義配置

### 修改標題文字

```html
<h3 class="end-meeting-title">您的自定義標題</h3>
```

### 修改按鈕文字和樣式

```html
<div class="end-meeting-actions">
    <button class="btn btn-cancel" onclick="yourCancelFunction()">
        取消
    </button>
    <button class="btn btn-danger" onclick="yourConfirmFunction()">
        確認
    </button>
</div>
```

### 自定義按鈕顏色

可以創建新的按鈕樣式類別：

```css
.btn-warning {
    background-color: var(--md-ref-palette-tertiary-600);
    color: var(--md-ref-palette-tertiary-100);
}

.btn-warning:hover:not(:disabled) {
    background-color: var(--md-ref-palette-tertiary-700);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}
```

## 🔧 進階功能

### 1. 鍵盤支援

組件自動支援 ESC 鍵關閉對話框：

```javascript
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        const overlay = document.getElementById('endMeetingOverlay');
        if (!overlay.classList.contains('hidden')) {
            hideEndMeetingDialog();
        }
    }
});
```

### 2. 點擊遮罩關閉

點擊對話框外部區域可關閉對話框：

```javascript
document.getElementById('endMeetingOverlay').addEventListener('click', function(e) {
    if (e.target === this) {
        hideEndMeetingDialog();
    }
});
```

### 3. 動畫自定義

可以通過 CSS 變數調整動畫時間：

```css
.end-meeting-overlay {
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.end-meeting-card {
    transition: transform 0.3s ease;
}
```

## 📱 響應式設計

組件在不同螢幕尺寸下的表現：

- **桌面端**：卡片居中顯示，按鈕水平排列
- **平板端**：卡片適當縮小，保持水平佈局
- **手機端**：按鈕垂直排列，增加觸控友好性

## 🌓 主題支援

組件完全支援明暗主題切換：

```css
/* 明亮主題 */
.light-theme .end-meeting-card {
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
}

/* 深色主題 */
.dark-theme .end-meeting-card {
    background-color: var(--md-sys-color-surface);
    color: var(--md-sys-color-on-surface);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
}
```

## 🔍 測試和演示

1. **完整演示**：打開 `components-demo.html` 查看所有組件
2. **獨立演示**：打開 `end-meeting-card-demo.html` 專注測試此組件
3. **自定義測試**：複製 HTML 結構到您的專案中進行測試

## 💡 最佳實踐

1. **確認操作**：對於重要操作，始終提供二次確認
2. **清晰標題**：使用明確的問句作為標題
3. **對比按鈕**：使用對比色區分取消和確認操作
4. **鍵盤支援**：確保所有互動都支援鍵盤操作
5. **無障礙性**：為按鈕添加適當的 aria 標籤

## 🐛 常見問題

**Q: 對話框沒有顯示動畫？**
A: 確保在移除 `hidden` 類別後使用 `setTimeout` 添加 `show` 類別。

**Q: 在移動設備上按鈕太小？**
A: 組件已包含響應式設計，確保引入了完整的 CSS 文件。

**Q: 如何修改動畫速度？**
A: 修改 CSS 中的 `transition` 屬性值。

## 📄 授權

此組件基於 Material 3 設計系統開發，遵循開源授權協議。
